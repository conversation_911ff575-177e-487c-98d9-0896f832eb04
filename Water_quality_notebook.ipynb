#Essential Libraries for performing analysis
import pandas as pd
import matplotlib.pyplot as plt
from matplotlib.colors import BoundaryNorm
from matplotlib.cm import get_cmap
import numpy as np
import seaborn as sns

#Machine Learing Libraries
from sklearn.model_selection import train_test_split
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier
from sklearn.model_selection import cross_val_score
from sklearn.metrics import accuracy_score, classification_report, confusion_matrix
from sklearn.preprocessing import StandardScaler
from sklearn.svm import SVC
from sklearn.tree import DecisionTreeClassifier
from xgboost import XGBClassifier
from sklearn.model_selection import GridSearchCV
from sklearn.inspection import permutation_importance

#Library to Save & Load Model
import joblib


df = pd.read_csv("water_quality_data.csv")

df.head()

df.shape

# Number of missing values in each column
for col in df.columns:
  print(col,"->",df[col].isna().sum())


# Replacing ph missing values with their mean
mean_value_1 =df['ph'].mean()
df['ph'].fillna(value=mean_value_1, inplace=True)

# Replacing Trihalomethanes missing values with their mean
mean_value_1 =df['Trihalomethanes'].mean()
df['Trihalomethanes'].fillna(value=mean_value_1, inplace=True)

# Replacing Sulfate missing values with their mean
mean_value_2 =df['Sulfate'].mean()
df['Sulfate'].fillna(value=mean_value_2, inplace=True)

print(df.shape)
for col in df.columns:
  print(col,"->",df[col].isna().sum())

# Summary of the dataframe
df.describe()

# Boxplot to visualize the 5-number summary and spot outliers
for col in df.columns:
  plt.boxplot(df[col])
  plt.title(col)
  plt.show()


#Checking outliers in each column that exceed the 1.5 threshold
Q1 = df.quantile(0.25)
Q3 = df.quantile(0.75)
IQR = Q3 - Q1
((df < (Q1 - 1.5 * IQR)) | (df > (Q3 + 1.5 * IQR))).sum()


#Dropping outliers
Q1 = df.quantile(0.25)
Q3 = df.quantile(0.75)
IQR = Q3 - Q1
df = df[~((df < (Q1 - 1.5 * IQR)) |(df > (Q3 + 1.5 * IQR))).any(axis=1)]
df

#To check distrubution of the features
for col in df.columns:
  sns.displot(df[col], kde=True, bins=15)

#To check the correlation between features
sns.heatmap(df.corr(), cmap="YlGnBu", annot=True)


#For correlation matrix w.r.t the target variable
for col in df:
  x = np.corrcoef(df[col],df['Potability'])
  print(x)

#Splitting the dataset and scaling it to improve the performance
x=df.drop(['Potability'], axis=1)
y=df['Potability']
X_train,X_test, y_train, y_test = train_test_split(x, y, test_size=0.25, random_state=42)
sc = StandardScaler()
X_train = sc.fit_transform(X_train)
X_test = sc.transform(X_test)

models = [
    RandomForestClassifier(),
    GradientBoostingClassifier(),
    SVC(),
    DecisionTreeClassifier(),
    XGBClassifier()  
]

# List to hold accuracy scores
accuracy = []

# Train and evaluate each model
for model in models:
    model.fit(X_train, y_train)
    y_pred = model.predict(X_test)
    accuracy.append(accuracy_score(y_test, y_pred))

# Add labels to bar plot
def addlabels(x, y):
    for i in range(len(x)):
        plt.text(i, y[i], f"{y[i]:.2f}", ha='center')

# Model names
model_names = ["Random Forest", "Gradient Boosting", "SVC", "Decision Tree", "XGBoost"]

# Plotting the accuracies
plt.figure(figsize=(10, 5))
plt.bar(model_names, accuracy, color='royalblue', width=0.4)
addlabels(model_names, accuracy)
plt.xlabel("Models")
plt.ylabel("Accuracy")
plt.title("Models vs Accuracy")
plt.ylim(0, 1)
plt.show()

# #Using grid search to optimize the hyperparameters of SVC
# param_grid = {'C': [0.1,1, 10, 100], 'gamma': [1,0.1,0.01,0.001],'kernel': ['rbf', 'poly', 'sigmoid']}
# grid = GridSearchCV(SVC(),param_grid,refit=True,verbose=2)
# grid.fit(X_train,y_train)
# print(grid.best_estimator_)


#Training the final model with optimized hyperparameters
model = SVC(C=1, gamma=0.1, kernel="rbf")
model.fit(X_train, y_train)
y_pred = model.predict(X_test)
accuracy_score(y_test, y_pred)

#Figuring out the feature importance
perm_importance = permutation_importance(model, X_test, y_test)

feature_names = df.columns
features = np.array(feature_names)

sorted_idx = perm_importance.importances_mean.argsort()
plt.barh(features[sorted_idx], perm_importance.importances_mean[sorted_idx])
plt.xlabel("Permutation Importance")

#Exporting the trained model as a pickle file for API integration

#joblib.dump(model, 'model.pkl')

